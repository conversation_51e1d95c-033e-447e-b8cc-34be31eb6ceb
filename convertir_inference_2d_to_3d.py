"""
Script pour convertir des PNG 2D (images) en volumes 3D NIfTI pour l'inférence avec nnU-Net.

Auteur : Gabriel Forest
Date   : 2025-06-16
"""

import os
import numpy as np
import nibabel as nib
from PIL import Image
from pathlib import Path
from natsort import natsorted


def convert_png_to_nifti_volumes_inference(images_root, output_imagesTs,
                                           file_ending=".nii.gz"):
    """
    Convertit des images PNG 2D en volumes 3D NIfTI pour l'inférence nnU-Net.
    Groupe les images par forme (shape) pour créer des volumes 3D homogènes.
    Chaque forme unique devient un volume 3D séparé.

    Args:
        images_root (str): Dossier contenant les images PNG
        output_imagesTs (str): Dossier de sortie `imagesTs`
        file_ending (str): Extension de fichier (par défaut .nii.gz)
    """
    images_root = Path(images_root)
    output_imagesTs = Path(output_imagesTs)
    output_imagesTs.mkdir(parents=True, exist_ok=True)

    # Récupérer et trier les PNG
    img_files = natsorted([f for f in os.listdir(images_root) if f.lower().endswith(".png")])
    if len(img_files) == 0:
        print("❌ Aucune image PNG trouvée.")
        return False

    print(f"📂 {len(img_files)} PNG trouvés dans {images_root}")

    # Analyser toutes les images pour grouper par forme
    print("🔍 Analyse des formes d'images pour groupement...")
    shape_groups = {}  # {shape: [list of file indices]}

    for i, img_file in enumerate(img_files):
        img_path = images_root / img_file
        img = np.array(Image.open(img_path).convert("L"))
        shape = img.shape

        if shape not in shape_groups:
            shape_groups[shape] = []
        shape_groups[shape].append(i)

    print(f"📊 Détection de {len(shape_groups)} formes d'images différentes:")
    for shape, indices in shape_groups.items():
        print(f"   📏 {shape}: {len(indices)} images")

    print(f"🔄 Création de {len(shape_groups)} volumes 3D (un par forme)...")

    vol_idx = 0
    for shape, indices in shape_groups.items():
        vol_idx += 1

        volume_img = []
        print(f"📏 Traitement du volume {vol_idx} - Forme: {shape} - {len(indices)} slices")

        # Charger toutes les images de cette forme
        for i in indices:
            img_path = images_root / img_files[i]
            img = np.array(Image.open(img_path).convert("L"))

            # Vérification de sécurité
            if img.shape != shape:
                print(f"⚠️ Forme inattendue pour {img_files[i]}: {img.shape} vs {shape}")
                continue

            volume_img.append(img)

        # Convertir en array numpy
        try:
            vol_img_np = np.stack(volume_img, axis=0)
        except ValueError as e:
            print(f"❌ Erreur lors de la création du volume {vol_idx}: {e}")
            continue

        # Nom du volume basé sur la forme
        vol_name = f"case_{shape[0]}x{shape[1]}_{vol_idx:03d}"
        output_path = output_imagesTs / f"{vol_name}_0000{file_ending}"

        nib.save(nib.Nifti1Image(vol_img_np, affine=np.eye(4)), output_path)
        print(f"✅ {output_path.name} créé avec {len(indices)} slices de forme {shape}")

    print(f"\n🎯 Tous les volumes ont été générés dans : {output_imagesTs}")
    return True


# ========================
# CONFIGURATION UTILISATEUR
# ========================

IMAGES_ROOT = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent"
OUTPUT_IMAGESTS = r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\inference_test4labeldifferent_3d"
FILE_ENDING = ".nii.gz"


if __name__ == "__main__":
    print("🔁 CONVERSION POUR INFÉRENCE nnU-Net")
    print("=" * 50)
    print(f"📂 Images source       : {IMAGES_ROOT}")
    print(f"📤 Dossier output      : {OUTPUT_IMAGESTS}")
    print("🔄 Mode: Un volume par forme d'image")
    print("=" * 50)

    success = convert_png_to_nifti_volumes_inference(
        images_root=IMAGES_ROOT,
        output_imagesTs=OUTPUT_IMAGESTS,
        file_ending=FILE_ENDING
    )

    if success:
        print("🎉 Conversion terminée avec succès.")
    else:
        print("💥 Échec de la conversion.")
