import os
import subprocess
from utils.conversion import convert_images_to_grayscale, convert_labels_to_classes
from utils.rename import rename_imagesTr, rename_labelsTr
from utils.verification import check_image, verify_dataset_integrity

# === CONFIGURATION GÉNÉRALE ===
DATASET_ID = "017"
CONFIGURATION = "3d_fullres"      # '2d' ou '3d_fullres'
FOLD = "all"              # 'all' ou '0-4'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5
GPU_ID = "0"
NUM_GPUS = 1

# === HYPERPARAMÈTRES ENTRAÎNEMENT ===
SAVE_NPZ = True
CONTINUE_TRAINING = False

# === PATHS ===
RAW_PATH = r"C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw"
PREPROCESSED_PATH = r"C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_preprocessed"
RESULTS_PATH = r"C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Results/nnUNet_results"

# === ENV VARS ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID
os.environ["nnUNet_n_proc_DA"] = "2"

def run(cmd):
    print(f"\n🟢 Lancement : {cmd}\n")
    subprocess.run(cmd, shell=True, check=True)

def main():
    """Fonction principale d'entraînement"""
    # === 0. Prétraitement images + masques (non destructif) ===
    # 🔍 Trouver automatiquement le dossier DatasetXXX_nom
    dataset_prefix = f"Dataset{DATASET_ID}_"
    dataset_name = next(
        (d for d in os.listdir(RAW_PATH) if d.startswith(dataset_prefix)),
        None
    )

    if dataset_name is None:
        raise FileNotFoundError(f"❌ Aucun dossier commençant par '{dataset_prefix}' trouvé dans {RAW_PATH}")

    # 📁 Construction des chemins dynamiques
    images_dir = os.path.join(RAW_PATH, dataset_name, "imagesTr")
    labels_dir = os.path.join(RAW_PATH, dataset_name, "labelsTr")

    print(f"📂 images_dir = {images_dir}")
    print(f"📂 labels_dir = {labels_dir}")

    verify_dataset_integrity(
        imagesTr_dir=images_dir,
        labelsTr_dir=labels_dir
    )

    rename_imagesTr(images_dir)
    rename_labelsTr(labels_dir)
    convert_images_to_grayscale(images_dir)  # optionnel : output_dir="..."
    convert_labels_to_classes(labels_dir, {
        0: 0,    # background
        29: 1,   # frontwall
        149: 2,  # backwall
        76: 3,  # flaw
        125: 4   # indication
    })  # optionnel : output_dir="..."

    # === 1. Preprocessing (plans + vérification) ===
    run(f'nnUNetv2_plan_and_preprocess -d {DATASET_ID} --verify_dataset_integrity')

    # === 2. Entraînement avec le bon Trainer prédéfini ===
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    train_cmd = f'nnUNetv2_train {DATASET_ID} {CONFIGURATION} {FOLD} -p {PLANS_NAME} -tr {trainer_class}'
    if SAVE_NPZ:
        train_cmd += " --npz"
    if CONTINUE_TRAINING:
        train_cmd += " --c"
    if NUM_GPUS > 1:
        train_cmd += f" -num_gpus {NUM_GPUS}"

    run(train_cmd)

if __name__ == "__main__":
    main()
