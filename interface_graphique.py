"""
Interface graphique pour utiliser les scripts de traitement d'images
Auteur: Gabriel Forest
Date: 2025-06-18
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import subprocess
import sys
from pathlib import Path
import threading

class ScriptGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Interface de Traitement d'Images")
        self.root.geometry("900x700")
        
        # Variables pour stocker les chemins
        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.labels_path = tk.StringVar()
        self.file_path = tk.StringVar()

        # Variables pour les scripts nnU-Net
        self.dataset_id = tk.StringVar()
        self.configuration = tk.StringVar()
        self.fold = tk.StringVar()
        self.epochs = tk.StringVar()
        self.gpu_id = tk.StringVar()
        self.save_npz = tk.BooleanVar()
        self.continue_training = tk.BooleanVar()
        self.trainer_name = tk.StringVar()
        self.plans_name = tk.StringVar()
        self.output_zip_name = tk.StringVar()

        # Variables pour autres scripts
        self.dataset_name = tk.StringVar()
        self.slices_per_volume = tk.StringVar()
        self.max_images = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Titre
        title_label = ttk.Label(main_frame, text="🔬 Interface de Traitement d'Images", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Sélection du script
        ttk.Label(main_frame, text="Choisir le script:", font=('Arial', 12, 'bold')).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.script_var = tk.StringVar()
        self.script_combo = ttk.Combobox(main_frame, textvariable=self.script_var, width=50, state="readonly")
        self.script_combo['values'] = [
            "🔍 Vérifier la forme d'un fichier NIfTI (check_shape.py)",
            "📦 Convertir dataset 2D vers 3D (convertir_dataset_2d_to_3d.py)",
            "🔄 Convertir inférence 2D vers 3D (convertir_inference_2d_to_3d.py)",
            "🔀 Transposer un fichier NIfTI (transpose_nifti.py)",
            "👁️ Visualiser un volume NIfTI (view_nifti_volume.py)",
            "🖼️ Visualiser dossier PNG (view_png_folder.py)",
            "📊 Analyser les labels (visualize_labels.py)",
            "🖼️ Visualiser un masque (visualize_mask.py)",
            "🔬 Visualiser fichiers NDE (view_nde_files.py)",
            "🚀 Entraîner modèle nnU-Net (train_nnunet.py)",
            "🔮 Inférence nnU-Net (infer_nnunet.py)",
            "📦 Exporter modèle ZIP (export_model_zip.py)"
        ]
        self.script_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        self.script_combo.bind('<<ComboboxSelected>>', self.on_script_change)
        
        # Frame pour les paramètres
        self.params_frame = ttk.LabelFrame(main_frame, text="Paramètres", padding="10")
        self.params_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        self.params_frame.columnconfigure(1, weight=1)
        
        # Frame pour les boutons
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=3, pady=10)
        
        # Bouton exécuter
        self.execute_btn = ttk.Button(buttons_frame, text="🚀 Exécuter", command=self.execute_script, 
                                     style='Accent.TButton')
        self.execute_btn.pack(side=tk.LEFT, padx=5)
        
        # Bouton aide
        help_btn = ttk.Button(buttons_frame, text="❓ Aide", command=self.show_help)
        help_btn.pack(side=tk.LEFT, padx=5)
        
        # Zone de sortie
        output_frame = ttk.LabelFrame(main_frame, text="Sortie du script", padding="5")
        output_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        output_frame.columnconfigure(0, weight=1)
        output_frame.rowconfigure(0, weight=1)
        
        self.output_text = scrolledtext.ScrolledText(output_frame, height=15, width=80)
        self.output_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configuration du redimensionnement
        main_frame.rowconfigure(4, weight=1)
        
    def clear_params_frame(self):
        """Efface tous les widgets du frame des paramètres"""
        for widget in self.params_frame.winfo_children():
            widget.destroy()
            
    def on_script_change(self, event=None):
        """Appelé quand l'utilisateur change de script"""
        self.clear_params_frame()
        script_name = self.script_var.get()
        
        if "check_shape" in script_name:
            self.setup_check_shape_params()
        elif "convertir_dataset_2d_to_3d" in script_name:
            self.setup_dataset_2d_to_3d_params()
        elif "convertir_inference_2d_to_3d" in script_name:
            self.setup_inference_2d_to_3d_params()
        elif "transpose_nifti" in script_name:
            self.setup_transpose_params()
        elif "view_nifti_volume" in script_name:
            self.setup_view_volume_params()
        elif "view_png_folder" in script_name:
            self.setup_png_folder_params()
        elif "visualize_labels" in script_name:
            self.setup_analyze_labels_params()
        elif "visualize_mask" in script_name:
            self.setup_visualize_mask_params()
        elif "view_nde_files" in script_name:
            self.setup_view_nde_params()
        elif "train_nnunet" in script_name:
            self.setup_train_nnunet_params()
        elif "infer_nnunet" in script_name:
            self.setup_infer_nnunet_params()
        elif "export_model_zip" in script_name:
            self.setup_export_model_params()
            
    def create_path_selector(self, parent, row, label_text, var, is_file=True, file_types=None):
        """Crée un sélecteur de chemin (fichier ou dossier)"""
        ttk.Label(parent, text=label_text).grid(row=row, column=0, sticky=tk.W, pady=2)
        
        entry = ttk.Entry(parent, textvariable=var, width=60)
        entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        
        if is_file:
            btn_text = "📁 Fichier"
            command = lambda: self.select_file(var, file_types)
        else:
            btn_text = "📂 Dossier"
            command = lambda: self.select_directory(var)
            
        btn = ttk.Button(parent, text=btn_text, command=command)
        btn.grid(row=row, column=2, pady=2)
        
    def select_file(self, var, file_types=None):
        """Ouvre un dialogue de sélection de fichier"""
        if file_types is None:
            file_types = [("Tous les fichiers", "*.*")]
        filename = filedialog.askopenfilename(filetypes=file_types)
        if filename:
            var.set(filename)
            
    def select_directory(self, var):
        """Ouvre un dialogue de sélection de dossier"""
        dirname = filedialog.askdirectory()
        if dirname:
            var.set(dirname)

    def setup_check_shape_params(self):
        """Configuration pour check_shape.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier NIfTI:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")])

    def setup_dataset_2d_to_3d_params(self):
        """Configuration pour convertir_dataset_2d_to_3d.py"""
        # Initialiser les valeurs par défaut
        self.dataset_name.set("Dataset_3d")

        self.create_path_selector(self.params_frame, 0, "Dossier images:", self.input_path, is_file=False)
        self.create_path_selector(self.params_frame, 1, "Dossier labels:", self.labels_path, is_file=False)
        self.create_path_selector(self.params_frame, 2, "Dossier sortie:", self.output_path, is_file=False)

        # Paramètres additionnels
        ttk.Label(self.params_frame, text="Nom du dataset:").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.dataset_name, width=30).grid(row=3, column=1, sticky=tk.W, pady=2)

        # Note explicative
        ttk.Label(self.params_frame, text="Mode: Un volume 3D par forme d'image unique",
                 font=('Arial', 8)).grid(row=4, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Format: 001_0000.nii.gz (images), 001.nii.gz (labels)",
                 font=('Arial', 8)).grid(row=5, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_inference_2d_to_3d_params(self):
        """Configuration pour convertir_inference_2d_to_3d.py"""
        self.create_path_selector(self.params_frame, 0, "Dossier images:", self.input_path, is_file=False)
        self.create_path_selector(self.params_frame, 1, "Dossier sortie:", self.output_path, is_file=False)

        # Note explicative
        ttk.Label(self.params_frame, text="Mode: Un volume 3D par forme d'image unique",
                 font=('Arial', 8)).grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=2)
        ttk.Label(self.params_frame, text="Format: 001_0000.nii.gz, 002_0000.nii.gz, ...",
                 font=('Arial', 8)).grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_transpose_params(self):
        """Configuration pour transpose_nifti.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier d'entrée:", self.input_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")])
        self.create_path_selector(self.params_frame, 1, "Fichier de sortie:", self.output_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")])

    def setup_view_volume_params(self):
        """Configuration pour view_nifti_volume.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier NIfTI:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NIfTI", "*.nii.gz"), ("Tous", "*.*")])

    def setup_png_folder_params(self):
        """Configuration pour view_png_folder.py"""
        # Initialiser les valeurs par défaut
        self.max_images.set("")

        self.create_path_selector(self.params_frame, 0, "Dossier PNG:", self.input_path, is_file=False)

        ttk.Label(self.params_frame, text="Nombre max d'images (optionnel):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.max_images, width=10).grid(row=1, column=1, sticky=tk.W, pady=2)

        # Note explicative
        ttk.Label(self.params_frame, text="(Laissez vide pour toutes les images)", font=('Arial', 8)).grid(row=2, column=1, sticky=tk.W, pady=2)

    def setup_analyze_labels_params(self):
        """Configuration pour visualize_labels.py"""
        self.create_path_selector(self.params_frame, 0, "Dossier labels:", self.input_path, is_file=False)

    def setup_visualize_mask_params(self):
        """Configuration pour visualize_mask.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier image:", self.file_path,
                                 is_file=True, file_types=[("Images PNG", "*.png"), ("Tous", "*.*")])

    def setup_view_nde_params(self):
        """Configuration pour view_nde_files.py"""
        self.create_path_selector(self.params_frame, 0, "Fichier NDE:", self.file_path,
                                 is_file=True, file_types=[("Fichiers NDE", "*.nde"), ("Fichiers HDF5", "*.h5"), ("Tous", "*.*")])

    def setup_train_nnunet_params(self):
        """Configuration pour train_nnunet.py"""
        # Initialiser les valeurs par défaut
        self.dataset_id.set("015")
        self.configuration.set("3d_fullres")
        self.fold.set("all")
        self.epochs.set("5")
        self.gpu_id.set("0")
        self.save_npz.set(True)
        self.continue_training.set(False)

        # Dataset ID
        ttk.Label(self.params_frame, text="Dataset ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.dataset_id, width=10).grid(row=0, column=1, sticky=tk.W, pady=2)

        # Configuration
        ttk.Label(self.params_frame, text="Configuration:").grid(row=1, column=0, sticky=tk.W, pady=2)
        config_combo = ttk.Combobox(self.params_frame, textvariable=self.configuration, width=15, state="readonly")
        config_combo['values'] = ["2d", "3d_fullres", "3d_lowres"]
        config_combo.grid(row=1, column=1, sticky=tk.W, pady=2)

        # Fold
        ttk.Label(self.params_frame, text="Fold:").grid(row=2, column=0, sticky=tk.W, pady=2)
        fold_combo = ttk.Combobox(self.params_frame, textvariable=self.fold, width=10, state="readonly")
        fold_combo['values'] = ["all", "0", "1", "2", "3", "4"]
        fold_combo.grid(row=2, column=1, sticky=tk.W, pady=2)

        # Epochs
        ttk.Label(self.params_frame, text="Epochs:").grid(row=3, column=0, sticky=tk.W, pady=2)
        epochs_combo = ttk.Combobox(self.params_frame, textvariable=self.epochs, width=10, state="readonly")
        epochs_combo['values'] = ["5", "10", "20", "50", "100"]
        epochs_combo.grid(row=3, column=1, sticky=tk.W, pady=2)

        # GPU ID
        ttk.Label(self.params_frame, text="GPU ID:").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.gpu_id, width=10).grid(row=4, column=1, sticky=tk.W, pady=2)

        # Chemins
        self.create_path_selector(self.params_frame, 5, "Dossier nnUNet_raw:", self.input_path, is_file=False)
        self.create_path_selector(self.params_frame, 6, "Dossier nnUNet_preprocessed:", self.output_path, is_file=False)
        self.create_path_selector(self.params_frame, 7, "Dossier nnUNet_results:", self.labels_path, is_file=False)

        # Options avancées
        ttk.Label(self.params_frame, text="Options:").grid(row=8, column=0, sticky=tk.W, pady=2)
        ttk.Checkbutton(self.params_frame, text="Sauvegarder NPZ", variable=self.save_npz).grid(row=8, column=1, sticky=tk.W, pady=2)
        ttk.Checkbutton(self.params_frame, text="Continuer entraînement", variable=self.continue_training).grid(row=9, column=1, sticky=tk.W, pady=2)

    def setup_infer_nnunet_params(self):
        """Configuration pour infer_nnunet.py"""
        # Initialiser les valeurs par défaut
        self.dataset_id.set("015")
        self.configuration.set("3d_fullres")
        self.fold.set("all")
        self.epochs.set("5")
        self.gpu_id.set("0")

        # Dataset ID
        ttk.Label(self.params_frame, text="Dataset ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.dataset_id, width=10).grid(row=0, column=1, sticky=tk.W, pady=2)

        # Configuration
        ttk.Label(self.params_frame, text="Configuration:").grid(row=1, column=0, sticky=tk.W, pady=2)
        config_combo = ttk.Combobox(self.params_frame, textvariable=self.configuration, width=15, state="readonly")
        config_combo['values'] = ["2d", "3d_fullres", "3d_lowres"]
        config_combo.grid(row=1, column=1, sticky=tk.W, pady=2)

        # Fold
        ttk.Label(self.params_frame, text="Fold:").grid(row=2, column=0, sticky=tk.W, pady=2)
        fold_combo = ttk.Combobox(self.params_frame, textvariable=self.fold, width=10, state="readonly")
        fold_combo['values'] = ["all", "0", "1", "2", "3", "4"]
        fold_combo.grid(row=2, column=1, sticky=tk.W, pady=2)

        # Epochs
        ttk.Label(self.params_frame, text="Epochs:").grid(row=3, column=0, sticky=tk.W, pady=2)
        epochs_combo = ttk.Combobox(self.params_frame, textvariable=self.epochs, width=10, state="readonly")
        epochs_combo['values'] = ["5", "10", "20", "50", "100"]
        epochs_combo.grid(row=3, column=1, sticky=tk.W, pady=2)

        # GPU ID
        ttk.Label(self.params_frame, text="GPU ID:").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.gpu_id, width=10).grid(row=4, column=1, sticky=tk.W, pady=2)

        # Dossier d'entrée (images à traiter)
        self.create_path_selector(self.params_frame, 5, "Dossier images d'entrée:", self.input_path, is_file=False)

        # Dossier de sortie de base
        self.create_path_selector(self.params_frame, 6, "Dossier de sortie de base:", self.output_path, is_file=False)

        # Chemins nnU-Net
        self.create_path_selector(self.params_frame, 7, "Dossier nnUNet_raw:", self.labels_path, is_file=False)

        # Note explicative
        ttk.Label(self.params_frame, text="Note: Le dossier de sortie final sera créé automatiquement",
                 font=('Arial', 8)).grid(row=8, column=0, columnspan=3, sticky=tk.W, pady=2)

    def setup_export_model_params(self):
        """Configuration pour export_model_zip.py"""
        # Initialiser les valeurs par défaut
        self.dataset_id.set("014")
        self.configuration.set("2d")
        self.fold.set("all")
        self.trainer_name.set("nnUNetTrainer_5epochs")
        self.plans_name.set("nnUNetPlans")
        self.output_zip_name.set("model_014_2d.zip")

        # Dataset ID
        ttk.Label(self.params_frame, text="Dataset ID:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.dataset_id, width=10).grid(row=0, column=1, sticky=tk.W, pady=2)

        # Configuration
        ttk.Label(self.params_frame, text="Configuration:").grid(row=1, column=0, sticky=tk.W, pady=2)
        config_combo = ttk.Combobox(self.params_frame, textvariable=self.configuration, width=15, state="readonly")
        config_combo['values'] = ["2d", "3d_fullres", "3d_lowres"]
        config_combo.grid(row=1, column=1, sticky=tk.W, pady=2)

        # Fold
        ttk.Label(self.params_frame, text="Fold:").grid(row=2, column=0, sticky=tk.W, pady=2)
        fold_combo = ttk.Combobox(self.params_frame, textvariable=self.fold, width=10, state="readonly")
        fold_combo['values'] = ["all", "0", "1", "2", "3", "4"]
        fold_combo.grid(row=2, column=1, sticky=tk.W, pady=2)

        # Trainer Name
        ttk.Label(self.params_frame, text="Trainer:").grid(row=3, column=0, sticky=tk.W, pady=2)
        trainer_combo = ttk.Combobox(self.params_frame, textvariable=self.trainer_name, width=25, state="readonly")
        trainer_combo['values'] = ["nnUNetTrainer_5epochs", "nnUNetTrainer_10epochs", "nnUNetTrainer_20epochs",
                                  "nnUNetTrainer_50epochs", "nnUNetTrainer_100epochs", "nnUNetTrainer"]
        trainer_combo.grid(row=3, column=1, sticky=tk.W, pady=2)

        # Plans Name
        ttk.Label(self.params_frame, text="Plans Name:").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.plans_name, width=20).grid(row=4, column=1, sticky=tk.W, pady=2)

        # Output ZIP Name
        ttk.Label(self.params_frame, text="Nom du fichier ZIP:").grid(row=5, column=0, sticky=tk.W, pady=2)
        ttk.Entry(self.params_frame, textvariable=self.output_zip_name, width=30).grid(row=5, column=1, sticky=tk.W, pady=2)

        # Note explicative
        ttk.Label(self.params_frame, text="Note: Le fichier ZIP sera créé dans le répertoire courant",
                 font=('Arial', 8)).grid(row=6, column=0, columnspan=3, sticky=tk.W, pady=2)

    def execute_script(self):
        """Exécute le script sélectionné avec les paramètres fournis"""
        script_name = self.script_var.get()
        if not script_name:
            messagebox.showerror("Erreur", "Veuillez sélectionner un script")
            return

        # Effacer la sortie précédente
        self.output_text.delete(1.0, tk.END)
        self.output_text.insert(tk.END, f"🚀 Exécution de {script_name}...\n\n")
        self.output_text.update()

        # Désactiver le bouton pendant l'exécution
        self.execute_btn.config(state='disabled')

        # Exécuter dans un thread séparé pour ne pas bloquer l'interface
        thread = threading.Thread(target=self._run_script_thread, args=(script_name,))
        thread.daemon = True
        thread.start()

    def _run_script_thread(self, script_name):
        """Exécute le script dans un thread séparé"""
        try:
            if "check_shape" in script_name:
                self._run_check_shape()
            elif "convertir_dataset_2d_to_3d" in script_name:
                self._run_dataset_2d_to_3d()
            elif "convertir_inference_2d_to_3d" in script_name:
                self._run_inference_2d_to_3d()
            elif "transpose_nifti" in script_name:
                self._run_transpose()
            elif "view_nifti_volume" in script_name:
                self._run_view_volume()
            elif "view_png_folder" in script_name:
                self._run_png_folder()
            elif "visualize_labels" in script_name:
                self._run_analyze_labels()
            elif "visualize_mask" in script_name:
                self._run_visualize_mask()
            elif "view_nde_files" in script_name:
                self._run_view_nde()
            elif "train_nnunet" in script_name:
                self._run_train_nnunet()
            elif "infer_nnunet" in script_name:
                self._run_infer_nnunet()
            elif "export_model_zip" in script_name:
                self._run_export_model()
        except Exception as e:
            self.append_output(f"[ERREUR] Erreur: {str(e)}\n")
        finally:
            # Réactiver le bouton de manière thread-safe
            try:
                self.root.after_idle(lambda: self._reactivate_button())
            except Exception:
                pass  # Ignorer les erreurs si la fenêtre est fermée

    def _reactivate_button(self):
        """Réactive le bouton d'exécution"""
        try:
            self.execute_btn.config(state='normal')
        except Exception:
            pass  # Ignorer si le widget n'existe plus

    def append_output(self, text):
        """Ajoute du texte à la zone de sortie de manière thread-safe"""
        try:
            if threading.current_thread() == threading.main_thread():
                # Si on est déjà dans le thread principal, pas besoin d'after()
                self._append_output_main_thread(text)
            else:
                # Sinon, utiliser after() pour passer au thread principal
                self.root.after_idle(lambda: self._append_output_main_thread(text))
        except Exception as e:
            print(f"Erreur dans append_output: {e}")

    def _append_output_main_thread(self, text):
        """Ajoute du texte à la zone de sortie (thread principal)"""
        try:
            self.output_text.insert(tk.END, text)
            self.output_text.see(tk.END)
            self.output_text.update_idletasks()  # Plus sûr que update()
        except Exception as e:
            print(f"Erreur dans _append_output_main_thread: {e}")

    def run_python_script(self, script_path, args=None):
        """Exécute un script Python avec des arguments"""
        cmd = [sys.executable, script_path]
        if args:
            cmd.extend(args)

        try:
            # Changer le répertoire de travail vers le répertoire du script
            script_dir = os.path.dirname(os.path.abspath(script_path))

            # Définir l'environnement avec l'encodage UTF-8
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=script_dir,
                env=env,
                encoding='utf-8',
                errors='replace'  # Remplace les caractères non décodables
            )

            # Lire la sortie ligne par ligne avec un délai pour éviter la récursion
            output_lines = []
            while True:
                line = process.stdout.readline()
                if line:
                    output_lines.append(line)
                    # Afficher par batch pour éviter trop d'appels à append_output
                    if len(output_lines) >= 5:  # Afficher par groupes de 5 lignes
                        self.append_output(''.join(output_lines))
                        output_lines = []
                elif process.poll() is not None:
                    break

            # Afficher les dernières lignes s'il y en a
            if output_lines:
                self.append_output(''.join(output_lines))

            process.wait()

            if process.returncode == 0:
                self.append_output("\n[SUCCES] Script exécuté avec succès!\n")
            else:
                self.append_output(f"\n[ERREUR] Script terminé avec le code d'erreur: {process.returncode}\n")

        except Exception as e:
            self.append_output(f"[ERREUR] Erreur lors de l'exécution: {str(e)}\n")

    def run_python_script_simple(self, script_path, args=None):
        """Exécute un script Python de manière simple (sans lecture en temps réel)"""
        cmd = [sys.executable, script_path]
        if args:
            cmd.extend(args)

        try:
            # Changer le répertoire de travail vers le répertoire du script
            script_dir = os.path.dirname(os.path.abspath(script_path))

            # Définir l'environnement avec l'encodage UTF-8
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # Exécuter et attendre la fin
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=script_dir,
                env=env,
                encoding='utf-8',
                errors='replace',
                timeout=300  # Timeout de 5 minutes
            )

            # Afficher toute la sortie d'un coup
            if result.stdout:
                self.append_output(result.stdout)
            if result.stderr:
                self.append_output(f"[STDERR] {result.stderr}")

            if result.returncode == 0:
                self.append_output("\n[SUCCES] Script exécuté avec succès!\n")
            else:
                self.append_output(f"\n[ERREUR] Script terminé avec le code d'erreur: {result.returncode}\n")

        except subprocess.TimeoutExpired:
            self.append_output("[ERREUR] Le script a dépassé le délai d'attente (5 minutes)\n")
        except Exception as e:
            self.append_output(f"[ERREUR] Erreur lors de l'exécution: {str(e)}\n")

    def _run_check_shape(self):
        """Exécute check_shape.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NIfTI\n")
            return

        # Créer un script temporaire avec le bon chemin
        temp_script = self._create_temp_check_shape_script(file_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_check_shape_script(self, file_path):
        """Crée un script temporaire pour check_shape avec le bon chemin"""
        temp_script = "temp_check_shape.py"
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(f'''
import nibabel as nib

def check_nifti_shape(file_path):
    try:
        nifti_img = nib.load(file_path)
        shape = nifti_img.shape
        print(f"Chemin du fichier: {{file_path}}")
        print(f"Forme de l'image: {{shape}}")
        print(f"Nombre de dimensions: {{len(shape)}}")
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier: {{str(e)}}")

if __name__ == "__main__":
    file_path = r"{file_path}"
    check_nifti_shape(file_path)
''')
        return temp_script

    def _run_dataset_2d_to_3d(self):
        """Exécute convertir_dataset_2d_to_3d.py"""
        images_path = self.input_path.get()
        labels_path = self.labels_path.get()
        output_path = self.output_path.get()
        dataset_name = self.dataset_name.get()

        if not all([images_path, labels_path, output_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_dataset_2d_to_3d_script(
            images_path, labels_path, output_path, dataset_name)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_dataset_2d_to_3d_script(self, images_path, labels_path, output_path, dataset_name):
        """Crée un script temporaire pour la conversion dataset 2D vers 3D"""
        temp_script = "temp_dataset_2d_to_3d.py"

        # Lire le script original et remplacer les chemins
        with open("convertir_dataset_2d_to_3d.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple pour éviter les erreurs d'encodage
        content = content.replace('🎯', '[INFO]')
        content = content.replace('📂', '[DOSSIER]')
        content = content.replace('🏷️', '[LABELS]')
        content = content.replace('📤', '[OUTPUT]')
        content = content.replace('📋', '[DATASET]')
        content = content.replace('�', '[MODE]')
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('✅', '[OK]')
        content = content.replace('⚠️', '[ATTENTION]')
        content = content.replace('🔧', '[CONFIG]')
        content = content.replace('📦', '[VOLUMES]')
        content = content.replace('📄', '[FICHIER]')
        content = content.replace('🎉', '[SUCCES]')
        content = content.replace('💥', '[ECHEC]')
        content = content.replace('🔍', '[ANALYSE]')
        content = content.replace('📊', '[STATS]')
        content = content.replace('📏', '[FORME]')

        # Remplacer les chemins hardcodés
        content = content.replace(
            'IMAGES_ROOT = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset014_test4labeldifferentUint8\\imagesTr"',
            f'IMAGES_ROOT = r"{images_path}"'
        )
        content = content.replace(
            'LABELS_ROOT = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset014_test4labeldifferentUint8\\labelsTr"',
            f'LABELS_ROOT = r"{labels_path}"'
        )
        content = content.replace(
            'OUTPUT_DIR = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw"',
            f'OUTPUT_DIR = r"{output_path}"'
        )
        content = content.replace(
            'DATASET_NAME = "Dataset015_test4labeldifferentUint8_3d"',
            f'DATASET_NAME = "{dataset_name}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_inference_2d_to_3d(self):
        """Exécute convertir_inference_2d_to_3d.py"""
        images_path = self.input_path.get()
        output_path = self.output_path.get()

        if not all([images_path, output_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_inference_2d_to_3d_script(images_path, output_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_inference_2d_to_3d_script(self, images_path, output_path):
        """Crée un script temporaire pour la conversion inférence 2D vers 3D"""
        temp_script = "temp_inference_2d_to_3d.py"

        with open("convertir_inference_2d_to_3d.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('🔄', '[CONVERSION]')
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('✅', '[OK]')
        content = content.replace('🎯', '[INFO]')
        content = content.replace('📂', '[DOSSIER]')
        content = content.replace('📤', '[OUTPUT]')
        content = content.replace('🎉', '[SUCCES]')
        content = content.replace('💥', '[ECHEC]')
        content = content.replace('�', '[CONVERSION]')
        content = content.replace('�', '[ANALYSE]')
        content = content.replace('📊', '[STATS]')
        content = content.replace('📏', '[FORME]')

        content = content.replace(
            'IMAGES_ROOT = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\inference_test4labeldifferent"',
            f'IMAGES_ROOT = r"{images_path}"'
        )
        content = content.replace(
            'OUTPUT_IMAGESTS = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\inference_test4labeldifferent_3d"',
            f'OUTPUT_IMAGESTS = r"{output_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_transpose(self):
        """Exécute transpose_nifti.py"""
        input_path = self.input_path.get()
        output_path = self.output_path.get()

        if not all([input_path, output_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_transpose_script(input_path, output_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_transpose_script(self, input_path, output_path):
        """Crée un script temporaire pour transpose_nifti"""
        temp_script = "temp_transpose.py"
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(f'''
import nibabel as nib
import numpy as np

def transpose_nifti(input_path, output_path):
    try:
        nifti_img = nib.load(input_path)
        data = nifti_img.get_fdata()
        affine = nifti_img.affine
        transposed_data = np.transpose(data, (1, 2, 0))
        new_nifti = nib.Nifti1Image(transposed_data, affine)
        nib.save(new_nifti, output_path)
        print(f"Image originale - Forme: {{data.shape}}")
        print(f"Image transposée - Forme: {{transposed_data.shape}}")
        print(f"Image sauvegardée dans: {{output_path}}")
    except Exception as e:
        print(f"Erreur lors du traitement: {{str(e)}}")

if __name__ == "__main__":
    input_path = r"{input_path}"
    output_path = r"{output_path}"
    transpose_nifti(input_path, output_path)
''')
        return temp_script

    def _run_view_volume(self):
        """Exécute view_nifti_volume.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NIfTI\n")
            return

        temp_script = self._create_temp_view_volume_script(file_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_view_volume_script(self, file_path):
        """Crée un script temporaire pour view_nifti_volume"""
        temp_script = "temp_view_volume.py"
        with open("view_nifti_volume.py", 'r', encoding='utf-8') as f:
            content = f.read()

        content = content.replace(
            'path_to_seg = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Results\\inference\\inference_test4labeldifferent_3d_v3\\case_001.nii.gz"',
            f'path_to_seg = r"{file_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_png_folder(self):
        """Exécute view_png_folder.py"""
        folder_path = self.input_path.get()
        max_images_str = self.max_images.get().strip()

        if not folder_path:
            self.append_output("[ERREUR] Veuillez sélectionner un dossier PNG\n")
            return

        # Traiter le nombre max d'images
        max_images = None
        if max_images_str:
            try:
                max_images = int(max_images_str)
            except ValueError:
                self.append_output("[ATTENTION] Nombre d'images invalide, toutes les images seront chargées\n")

        temp_script = self._create_temp_png_folder_script(folder_path, max_images)
        self.run_python_script_simple(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass

    def _create_temp_png_folder_script(self, folder_path, max_images):
        """Crée un script temporaire pour view_png_folder"""
        temp_script = "temp_png_folder.py"

        script_content = f'''
import sys
import os
sys.path.append(r"{os.getcwd()}")

from view_png_folder import PNGFolderViewer

try:
    print("Lancement du visualiseur PNG...")
    max_imgs = {max_images} if {max_images is not None} else None
    viewer = PNGFolderViewer(r"{folder_path}", max_images=max_imgs)
    print("Visualiseur fermé.")
except Exception as e:
    print(f"Erreur: {{e}}")
'''

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(script_content)

        return temp_script

    def _run_analyze_labels(self):
        """Exécute visualize_labels.py"""
        labels_dir = self.input_path.get()
        if not labels_dir:
            self.append_output("[ERREUR] Veuillez sélectionner un dossier de labels\n")
            return

        temp_script = self._create_temp_analyze_labels_script(labels_dir)
        # Utiliser la méthode simple pour éviter les problèmes de récursion
        self.run_python_script_simple(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass  # Ignorer si le fichier n'existe plus

    def _create_temp_analyze_labels_script(self, labels_dir):
        """Crée un script temporaire pour visualize_labels"""
        temp_script = "temp_analyze_labels.py"
        with open("visualize_labels.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('📁', '[DOSSIER]')

        content = content.replace(
            'labels_dir = Path(r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset012_test4labeldifferent\\labelsTr_backup")',
            f'labels_dir = Path(r"{labels_dir}")'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_visualize_mask(self):
        """Exécute visualize_mask.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier image\n")
            return

        temp_script = self._create_temp_visualize_mask_script(file_path)
        self.run_python_script(temp_script)
        os.remove(temp_script)

    def _create_temp_visualize_mask_script(self, file_path):
        """Crée un script temporaire pour visualize_mask"""
        temp_script = "temp_visualize_mask.py"
        with open("visualize_mask.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les emojis par du texte simple
        content = content.replace('❌', '[ERREUR]')
        content = content.replace('→', '->')

        content = content.replace(
            'image_path = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw\\Dataset012_test4labeldifferent\\labelsTr_backup\\0001.png"',
            f'image_path = r"{file_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def show_help(self):
        """Affiche l'aide pour l'utilisation de l'interface"""
        help_text = """
🔬 AIDE - Interface de Traitement d'Images

📋 SCRIPTS DISPONIBLES:

🔍 Vérifier la forme d'un fichier NIfTI
   - Affiche les dimensions d'un fichier .nii.gz
   - Paramètres: Fichier NIfTI d'entrée

📦 Convertir dataset 2D vers 3D
   - Convertit des images PNG 2D en volumes 3D NIfTI pour nnU-Net
   - Paramètres: Dossier images, dossier labels, dossier sortie, nom dataset, slices par volume

🔄 Convertir inférence 2D vers 3D
   - Convertit des images PNG 2D en volumes 3D pour l'inférence
   - Paramètres: Dossier images, dossier sortie, slices par volume

🔀 Transposer un fichier NIfTI
   - Change l'orientation des axes d'un volume NIfTI
   - Paramètres: Fichier d'entrée, fichier de sortie

👁️ Visualiser un volume NIfTI
   - Affiche les slices d'un volume NIfTI avec colormap
   - Paramètres: Fichier NIfTI

🎨 Visualiser 10 masques
   - Affiche plusieurs masques PNG avec couleurs
   - Paramètres: Dossier masques, nombre d'images

📊 Analyser les labels
   - Analyse les valeurs uniques dans les images de labels
   - Paramètres: Dossier labels

🖼️ Visualiser un masque
   - Visualise un masque PNG avec normalisation
   - Paramètres: Fichier image

� Visualiser fichiers NDE
   - Visualise les fichiers NDE avec correction d'orientation
   - Paramètres: Fichier NDE

🚀 Entraîner modèle nnU-Net
   - Lance l'entraînement d'un modèle nnU-Net
   - Paramètres: Dataset ID, configuration, fold, epochs, GPU, chemins

🔮 Inférence nnU-Net
   - Effectue l'inférence avec un modèle nnU-Net entraîné
   - Paramètres: Dataset ID, configuration, dossiers d'entrée/sortie

📦 Exporter modèle ZIP
   - Exporte un modèle nnU-Net entraîné en fichier ZIP
   - Paramètres: Dataset ID, configuration, trainer, nom du fichier

�💡 UTILISATION:
1. Sélectionnez un script dans la liste déroulante
2. Remplissez les paramètres requis
3. Cliquez sur "Exécuter"
4. Consultez la sortie dans la zone de texte

⚠️ NOTES:
- Les chemins peuvent être saisis manuellement ou sélectionnés via les boutons
- L'exécution se fait en arrière-plan, l'interface reste responsive
- Les scripts temporaires sont automatiquement nettoyés après exécution
        """

        # Créer une nouvelle fenêtre pour l'aide
        help_window = tk.Toplevel(self.root)
        help_window.title("Aide")
        help_window.geometry("800x600")

        # Zone de texte avec scrollbar
        help_frame = ttk.Frame(help_window, padding="10")
        help_frame.pack(fill=tk.BOTH, expand=True)

        help_text_widget = scrolledtext.ScrolledText(help_frame, wrap=tk.WORD, font=('Consolas', 10))
        help_text_widget.pack(fill=tk.BOTH, expand=True)
        help_text_widget.insert(tk.END, help_text)
        help_text_widget.config(state=tk.DISABLED)

    def _run_view_nde(self):
        """Exécute view_nde_files.py"""
        file_path = self.file_path.get()
        if not file_path:
            self.append_output("❌ Veuillez sélectionner un fichier NDE\n")
            return

        self.append_output("🔬 Ouverture du visualiseur NDE interactif...\n")
        self.append_output("📋 Contrôles disponibles :\n")
        self.append_output("  ← → (ou A/D) : Slice précédente/suivante\n")
        self.append_output("  ↑ ↓ (ou W/S) : Dataset précédent/suivant\n")
        self.append_output("  Home/End     : Première/dernière slice\n")
        self.append_output("  I            : Informations détaillées\n")
        self.append_output("  H            : Afficher l'aide\n")
        self.append_output("  Q/Escape     : Quitter\n")
        self.append_output("📊 Détection automatique des datasets d'amplitude (int16/uint16)\n")
        self.append_output("✅ Orientation corrigée et démarrage à la première slice\n")
        self.append_output("🔍 Fermez la fenêtre pour continuer\n\n")

        # Créer un script temporaire avec le visualiseur NDE
        temp_script = self._create_temp_view_nde_script(file_path)
        self.run_python_script_simple(temp_script)

        try:
            os.remove(temp_script)
        except:
            pass

    def _create_temp_view_nde_script(self, file_path):
        """Crée un script temporaire pour view_nde_files avec le bon chemin"""
        temp_script = "temp_view_nde.py"
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(f'''
import sys
import os
sys.path.append(r"{os.getcwd()}")

from view_nde_files_stable import NDEViewerStable

try:
    print("Lancement du visualiseur NDE...")
    viewer = NDEViewerStable(r"{file_path}")
    viewer.show()
    print("Visualiseur NDE fermé.")
except Exception as e:
    print(f"Erreur: {{e}}")
    import traceback
    traceback.print_exc()
''')
        return temp_script

    def _run_train_nnunet(self):
        """Exécute train_nnunet.py"""
        dataset_id = self.dataset_id.get()
        configuration = self.configuration.get()
        fold = self.fold.get()
        epochs = self.epochs.get()
        gpu_id = self.gpu_id.get()
        raw_path = self.input_path.get()
        preprocessed_path = self.output_path.get()
        results_path = self.labels_path.get()
        save_npz = self.save_npz.get()
        continue_training = self.continue_training.get()

        if not all([dataset_id, configuration, fold, epochs, raw_path, preprocessed_path, results_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_train_nnunet_script(
            dataset_id, configuration, fold, epochs, gpu_id,
            raw_path, preprocessed_path, results_path, save_npz, continue_training)
        self.run_python_script(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass

    def _create_temp_train_nnunet_script(self, dataset_id, configuration, fold, epochs, gpu_id,
                                        raw_path, preprocessed_path, results_path, save_npz, continue_training):
        """Crée un script temporaire pour train_nnunet"""
        temp_script = "temp_train_nnunet.py"

        with open("train_nnunet.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les paramètres
        content = content.replace('DATASET_ID = "015"', f'DATASET_ID = "{dataset_id}"')
        content = content.replace('CONFIGURATION = "3d_fullres"', f'CONFIGURATION = "{configuration}"')
        content = content.replace('FOLD = "all"', f'FOLD = "{fold}"')
        content = content.replace('EPOCHS = 5', f'EPOCHS = {epochs}')
        content = content.replace('GPU_ID = "0"', f'GPU_ID = "{gpu_id}"')
        content = content.replace('SAVE_NPZ = True', f'SAVE_NPZ = {save_npz}')
        content = content.replace('CONTINUE_TRAINING = False', f'CONTINUE_TRAINING = {continue_training}')

        # Remplacer les chemins
        content = content.replace(
            'RAW_PATH = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw"',
            f'RAW_PATH = r"{raw_path}"'
        )
        content = content.replace(
            'PREPROCESSED_PATH = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_preprocessed"',
            f'PREPROCESSED_PATH = r"{preprocessed_path}"'
        )
        content = content.replace(
            'RESULTS_PATH = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Results\\nnUNet_results"',
            f'RESULTS_PATH = r"{results_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_infer_nnunet(self):
        """Exécute infer_nnunet.py"""
        dataset_id = self.dataset_id.get()
        configuration = self.configuration.get()
        fold = self.fold.get()
        epochs = self.epochs.get()
        gpu_id = self.gpu_id.get()
        input_folder = self.input_path.get()
        base_output_root = self.output_path.get()
        raw_path = self.labels_path.get()

        if not all([dataset_id, configuration, fold, epochs, input_folder, base_output_root, raw_path]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_infer_nnunet_script(
            dataset_id, configuration, fold, epochs, gpu_id,
            input_folder, base_output_root, raw_path)
        self.run_python_script(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass

    def _create_temp_infer_nnunet_script(self, dataset_id, configuration, fold, epochs, gpu_id,
                                        input_folder, base_output_root, raw_path):
        """Crée un script temporaire pour infer_nnunet"""
        temp_script = "temp_infer_nnunet.py"

        with open("infer_nnunet.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les paramètres
        content = content.replace('DATASET_ID = "015"', f'DATASET_ID = "{dataset_id}"')
        content = content.replace('CONFIGURATION = "3d_fullres"', f'CONFIGURATION = "{configuration}"')
        content = content.replace('FOLD = "all"', f'FOLD = "{fold}"')
        content = content.replace('EPOCHS = 5', f'EPOCHS = {epochs}')
        content = content.replace('GPU_ID = "0"', f'GPU_ID = "{gpu_id}"')

        # Remplacer les chemins
        content = content.replace(
            'INPUT_FOLDER = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\inference_test4labeldifferent_uint8"',
            f'INPUT_FOLDER = r"{input_folder}"'
        )
        content = content.replace(
            'BASE_OUTPUT_ROOT = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Results\\inference"',
            f'BASE_OUTPUT_ROOT = r"{base_output_root}"'
        )
        content = content.replace(
            'RAW_PATH = r"C:\\Users\\<USER>\\OneDrive - EvidentScientific\\Documents\\4Corrosion\\Dataset\\nnUnet\\nnUNet_raw"',
            f'RAW_PATH = r"{raw_path}"'
        )

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script

    def _run_export_model(self):
        """Exécute export_model_zip.py"""
        dataset_id = self.dataset_id.get()
        configuration = self.configuration.get()
        fold = self.fold.get()
        trainer_name = self.trainer_name.get()
        plans_name = self.plans_name.get()
        output_zip_name = self.output_zip_name.get()

        if not all([dataset_id, configuration, fold, trainer_name, plans_name, output_zip_name]):
            self.append_output("❌ Veuillez remplir tous les champs obligatoires\n")
            return

        temp_script = self._create_temp_export_model_script(
            dataset_id, configuration, fold, trainer_name, plans_name, output_zip_name)
        self.run_python_script(temp_script)
        try:
            os.remove(temp_script)
        except Exception:
            pass

    def _create_temp_export_model_script(self, dataset_id, configuration, fold, trainer_name, plans_name, output_zip_name):
        """Crée un script temporaire pour export_model_zip"""
        temp_script = "temp_export_model.py"

        with open("export_model_zip.py", 'r', encoding='utf-8') as f:
            content = f.read()

        # Remplacer les paramètres
        content = content.replace('DATASET_ID = "014"', f'DATASET_ID = "{dataset_id}"')
        content = content.replace('CONFIGURATION = "2d"', f'CONFIGURATION = "{configuration}"')
        content = content.replace('FOLD = "all"', f'FOLD = "{fold}"')
        content = content.replace('TRAINER_NAME = "nnUNetTrainer_5epochs"', f'TRAINER_NAME = "{trainer_name}"')
        content = content.replace('PLANS_NAME = "nnUNetPlans"', f'PLANS_NAME = "{plans_name}"')
        content = content.replace('OUTPUT_ZIP_NAME = f"model_{DATASET_ID}_{CONFIGURATION}.zip"', f'OUTPUT_ZIP_NAME = "{output_zip_name}"')

        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(content)

        return temp_script


def main():
    """Fonction principale"""
    root = tk.Tk()
    app = ScriptGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
